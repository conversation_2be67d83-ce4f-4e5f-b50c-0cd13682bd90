// Copyright © 2015 The CefSharp Authors. All rights reserved.
//
// Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.

namespace CefSharp.Enums
{
    /// <summary>
    /// Cursor type values.
    /// </summary>
    public enum CursorType
    {
        /// <summary>
        /// Pointer
        /// </summary>
        Pointer = 0,
        /// <summary>
        /// An enum constant representing the cross option.
        /// </summary>
        Cross,
        /// <summary>
        /// An enum constant representing the hand option.
        /// </summary>
        Hand,
        /// <summary>
        /// An enum constant representing the beam option.
        /// </summary>
        IBeam,
        /// <summary>
        /// An enum constant representing the wait option.
        /// </summary>
        Wait,
        /// <summary>
        /// An enum constant representing the help option.
        /// </summary>
        Help,
        /// <summary>
        /// An enum constant representing the east resize option.
        /// </summary>
        EastResize,
        /// <summary>
        /// An enum constant representing the north resize option.
        /// </summary>
        NorthResize,
        /// <summary>
        /// An enum constant representing the northeast resize option.
        /// </summary>
        NortheastResize,
        /// <summary>
        /// An enum constant representing the northwest resize option.
        /// </summary>
        NorthwestResize,
        /// <summary>
        /// An enum constant representing the south resize option.
        /// </summary>
        SouthResize,
        /// <summary>
        /// An enum constant representing the southeast resize option.
        /// </summary>
        SoutheastResize,
        /// <summary>
        /// An enum constant representing the southwest resize option.
        /// </summary>
        SouthwestResize,
        /// <summary>
        /// An enum constant representing the west resize option.
        /// </summary>
        WestResize,
        /// <summary>
        /// An enum constant representing the north south resize option.
        /// </summary>
        NorthSouthResize,
        /// <summary>
        /// An enum constant representing the east west resize option.
        /// </summary>
        EastWestResize,
        /// <summary>
        /// An enum constant representing the northeast southwest resize option.
        /// </summary>
        NortheastSouthwestResize,
        /// <summary>
        /// An enum constant representing the northwest southeast resize option.
        /// </summary>
        NorthwestSoutheastResize,
        /// <summary>
        /// An enum constant representing the column resize option.
        /// </summary>
        ColumnResize,
        /// <summary>
        /// An enum constant representing the row resize option.
        /// </summary>
        RowResize,
        /// <summary>
        /// An enum constant representing the middle panning option.
        /// </summary>
        MiddlePanning,
        /// <summary>
        /// An enum constant representing the east panning option.
        /// </summary>
        EastPanning,
        /// <summary>
        /// An enum constant representing the north panning option.
        /// </summary>
        NorthPanning,
        /// <summary>
        /// An enum constant representing the northeast panning option.
        /// </summary>
        NortheastPanning,
        /// <summary>
        /// An enum constant representing the northwest panning option.
        /// </summary>
        NorthwestPanning,
        /// <summary>
        /// An enum constant representing the south panning option.
        /// </summary>
        SouthPanning,
        /// <summary>
        /// An enum constant representing the southeast panning option.
        /// </summary>
        SoutheastPanning,
        /// <summary>
        /// An enum constant representing the southwest panning option.
        /// </summary>
        SouthwestPanning,
        /// <summary>
        /// An enum constant representing the west panning option.
        /// </summary>
        WestPanning,
        /// <summary>
        /// An enum constant representing the move option.
        /// </summary>
        Move,
        /// <summary>
        /// An enum constant representing the vertical text option.
        /// </summary>
        VerticalText,
        /// <summary>
        /// An enum constant representing the cell option.
        /// </summary>
        Cell,
        /// <summary>
        /// An enum constant representing the context menu option.
        /// </summary>
        ContextMenu,
        /// <summary>
        /// An enum constant representing the alias option.
        /// </summary>
        Alias,
        /// <summary>
        /// An enum constant representing the progress option.
        /// </summary>
        Progress,
        /// <summary>
        /// An enum constant representing the no drop option.
        /// </summary>
        NoDrop,
        /// <summary>
        /// An enum constant representing the copy option.
        /// </summary>
        Copy,
        /// <summary>
        /// An enum constant representing the none option.
        /// </summary>
        None,
        /// <summary>
        /// An enum constant representing the not allowed option.
        /// </summary>
        NotAllowed,
        /// <summary>
        /// An enum constant representing the zoom in option.
        /// </summary>
        ZoomIn,
        /// <summary>
        /// An enum constant representing the zoom out option.
        /// </summary>
        ZoomOut,
        /// <summary>
        /// An enum constant representing the grab option.
        /// </summary>
        Grab,
        /// <summary>
        /// An enum constant representing the grabbing option.
        /// </summary>
        Grabbing,
        /// <summary>
        /// An enum constant representing the MiddlePanningVertical option.
        /// </summary>
        MiddlePanningVertical,
        /// <summary>
        /// An enum constant representing the MiddlePanningHorizontal option.
        /// </summary>
        MiddlePanningHorizontal,
        /// <summary>
        /// An enum constant representing the custom option.
        /// </summary>
        Custom,
        /// <summary>
        /// DndNone
        /// </summary>
        DndNone,
        /// <summary>
        /// DndMove
        /// </summary>
        DndMove,
        /// <summary>
        /// DndCopy
        /// </summary>
        DndCopy,
        /// <summary>
        /// DndLink
        /// </summary>
        DndLink
    }
}
