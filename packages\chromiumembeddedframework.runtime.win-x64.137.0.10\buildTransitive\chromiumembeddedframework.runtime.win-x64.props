﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--
    Files are no longer copied as part of this targets file.
    For projects other than CefSharp define a target to copy the files.
    Checkout the CefSharp.Common.targets file for an example
    -->
  
  <ItemGroup>
	<CefRuntimeWin64 Include="$(MSBuildThisFileDirectory)..\runtimes\win-x64\native\*.*" />
	<_CefRuntimeWin64Additional Include="$(MSBuildThisFileDirectory)..\CEF\win-x64\**\*.*" />
	
	<CefRedist64 Include="@(CefRuntimeWin64)" />
	<CefRedist64 Include="@(_CefRuntimeWin64Additional)" />
  </ItemGroup>
  
  <Target Name="CefRedist64CopyResources">
    <PropertyGroup>
      <CefRedist64TargetDir Condition="'$(CefRedist64TargetDir)' == ''">$(TargetDir)</CefRedist64TargetDir>
    </PropertyGroup>
    <!--
    Cannot gurantee access to the main CefRuntimeWin64 ItemGroup here
    so we create a duplicate inside our target.
    https://github.com/dotnet/project-system/issues/4158
    -->
    <ItemGroup>
      <_CefRuntimeWin64 Include="$(MSBuildThisFileDirectory)..\runtimes\win-x64\native\*.*" />
	  <_CefRuntimeWin64 Include="$(MSBuildThisFileDirectory)..\CEF\win-x64\**\*.*" />
    </ItemGroup>
    <Message Importance="high" Text="Copying Chromium Embedded Framework Runtime x64 files from $(MSBuildThisFileDirectory)..\runtimes\win-x64\native to $(CefRedist64TargetDir)" />
    <Copy SourceFiles="@(_CefRuntimeWin64)" DestinationFiles="@(_CefRuntimeWin64->'$(CefRedist64TargetDir)\%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" />
  </Target>
</Project>