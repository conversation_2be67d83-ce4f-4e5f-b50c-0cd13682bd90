// Copyright © 2015 The CefSharp Authors. All rights reserved.
//
// Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.

namespace CefSharp.Internals
{
    public static class CefSharpArguments
    {
        public const string WcfEnabledArgument = "--wcf-enabled";
        public const string HostProcessIdArgument = "--host-process-id";
        public const string CustomSchemeArgument = "--custom-scheme";
        public const string FocusedNodeChangedEnabledArgument = "--focused-node-enabled";
        public const string SubProcessTypeArgument = "--type";
        public const string ExitIfParentProcessClosed = "--cefsharpexitsub";
    }
}
