# AntdUI

### 🦄 介绍

基于 Ant Design 设计语言的 WinForm UI 界面库，致力于将现代美观的前端设计风格带入到桌面应用程序中。采用纯GDI绘图，不需任何图片资源，全面支持AOT，最低兼容 `.NET Framework 4.0`

### ✨ 特性

- 🌈 现代化的设计风格
- 🎨 精细绘制与流畅动画
- 🚀 在 Winform 上呈现最佳阴影效果
- 📦 无边框窗口，保留原生窗口特性
- 💎 3D翻转特效
- 👚 主题自定义
- 🦜 SVG 矢量图
- 👓 DPI 自适应
- 🌍 全球化支持

### 🖥 环境

- .NET 9.0
- .NET 6.0
- .NET Framework4.8
- .NET Framework4.0及以上

### 🌴 控件

⬇️| 通用 `2` | 动画 | 禁用 |
:---:|:--|:--:|:--:|
➡️| [**Button** 按钮](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Button.md) | ✅ | ✅ |
➡️| [**FloatButton** 悬浮按钮](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/FloatButton.md) | ✅ | ❎ |
||||
⬇️| 布局 `5` | 动画 | 禁用 |
➡️| [**Divider** 分割线](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Divider.md) | ❎ | ❎ |
➡️| [**StackPanel** 堆栈布局](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/StackPanel.md) | ❎ | ❎ |
➡️| [**FlowPanel** 流动布局](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/FlowPanel.md) | ❎ | ❎ |
➡️| [**GridPanel** 格栅布局](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/GridPanel.md) | ❎ | ❎ |
➡️| [**Splitter** 分隔面板](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Splitter.md) | ❎ | ❎ |
||||
⬇️| 导航 `6` | 动画 | 禁用 |
➡️| [**Breadcrumb** 面包屑](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Breadcrumb.md) | ✅ | ❎ |
➡️| [**Dropdown** 下拉菜单](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Dropdown.md) | ✅ | ✅ |
➡️| [**Menu** 导航菜单](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Menu.md) | ✅ | ❎ |
➡️| [**PageHeader** 页头](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/PageHeader.md) | ✅ | ❎ |
➡️| [**Pagination** 分页](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Pagination.md) | ✅ | ✅ |
➡️| [**Steps** 步骤条](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Steps.md) | ❎ | ❎ |
||||
⬇️| 数据录入 `13` | 动画 | 禁用 |
➡️| [**Checkbox** 多选框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Checkbox.md) | ✅ | ✅ |
➡️| [**ColorPicker** 颜色选择器](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/ColorPicker.md) | ✅ | ✅ |
➡️| [**DatePicker** 日期选择框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/DatePicker.md) | ✅ | ✅ |
➡️| [**DatePickerRange** 日期范围选择框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/DatePicker#DatePickerRange.md) | ✅ | ✅ |
➡️| [**Input** 输入框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Input.md) | ✅ | ✅ |
➡️| [**InputNumber** 数字输入框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Input#InputNumber.md) | ✅ | ✅ |
➡️| [**Radio** 单选框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Radio.md) | ✅ | ✅ |
➡️| [**Rate** 评分](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Rate.md) | ✅ | ❎ |
➡️| [**Select** 选择器](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Select.md) | ✅ | ✅ |
➡️| [**Slider** 滑动输入条](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Slider.md) | ✅ | ❎ |
➡️| [**Switch** 开关](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Switch.md) | ✅ | ✅ |
➡️| [**TimePicker** 时间选择框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/TimePicker.md) | ✅ | ✅ |
➡️| [**UploadDragger** 拖拽上传](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/UploadDragger.md) | ✅ | ❎ |
||||
⬇️| 数据展示 `18` | 动画 | 禁用 |
➡️| [**Avatar** 头像](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Avatar.md) | ❎ | ❎ |
➡️| [**Badge** 徽标数](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Badge.md) | ✅ | ❎ |
➡️| [**Calendar** 日历](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Calendar.md) | ✅ | ❎ |
➡️| [**Panel** 面板](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Panel.md) | ✅ | ❎ |
➡️| [**Carousel** 走马灯](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Carousel.md) | ✅ | ❎ |
➡️| [**Collapse** 折叠面板](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Collapse.md) | ✅ | ❎ |
➡️| [**Preview** 图片预览](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Preview.md) | ✅ | ✅ |
➡️| [**Popover** 气泡卡片](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Popover.md) | ✅ | ❎ |
➡️| [**Segmented** 分段控制器](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Segmented.md) | ✅ | ✅ |
➡️| [**Table** 表格](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Table.md) | ✅ | ❎ |
➡️| [**Tabs** 标签页](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Tabs.md) | ✅ | ✅ |
➡️| [**Tag** 标签](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Tag.md) | ✅ | ❎ |
➡️| [**Timeline** 时间轴](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Timeline.md) | ❎ | ❎ |
➡️| [**Tooltip** 文字提示](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Tooltip.md) | ✅ | ❎ |
➡️| [**Tree** 树形控件](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Tree.md) | ✅ | ✅ |
➡️| [**Tour** 漫游式引导](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Tour.md) | ✅ | ❎ |
➡️| [**Label** 文本](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Label.md) | ✅ | ❎ |
➡️| [**LabelTime** 时间文本](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/LabelTime.md) | ✅ | ❎ |
||||
⬇️| 反馈 `7` | 动画 | 禁用 |
➡️| [**Alert** 警告提示](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Alert.md) | ✅ | ❎ |
➡️| [**Drawer** 抽屉](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Drawer.md) | ✅ | ❎ |
➡️| [**Message** 全局提示](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Message.md) | ✅ | ❎ |
➡️| [**Modal** 对话框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Modal.md) | ✅ | ❎ |
➡️| [**Notification** 通知提醒框](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Notification.md) | ✅ | ❎ |
➡️| [**Progress** 进度条](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Progress.md) | ✅ | ❎ |
➡️| [**Spin** 加载中](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Spin.md) | ✅ | ❎ |
||||
⬇️| 聊天 `2` | 动画 | 禁用 |
➡️| **MsgList** 好友消息列表 | ✅ | ❎ |
➡️| **ChatList** 气泡聊天列表 | ✅ | ❎ |
||||
⬇️| 其他 `4` | 动画 | 禁用 |
➡️| [**Battery** 电量](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Battery.md) | ✅ | ❎ |
➡️| [**Signal** 信号强度](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/Signal.md) | ✅ | ❎ |
➡️| [**ContextMenuStrip** 右键菜单](https://gitee.com/AntdUI/AntdUI/blob/main/doc/wiki/zh/Control/ContextMenuStrip.md) | ✅ | ❎ |
➡️| **Image3D** 图片3D | ✅ | ❎ |