using System;
using System.Reflection;
using System.Runtime.InteropServices;
using CefSharp;

[assembly:AssemblyTitle("CefSharp.Core")]
[assembly:AssemblyDescription("CefSharp Core Library")]
[assembly:AssemblyCompany(AssemblyInfo.AssemblyCompany)]
[assembly:AssemblyProduct(AssemblyInfo.AssemblyProduct)]
[assembly:AssemblyCopyright(AssemblyInfo.AssemblyCopyright)]

[assembly:AssemblyVersion(AssemblyInfo.AssemblyVersion)]
[assembly:ComVisible(AssemblyInfo.ComVisible)]
[assembly:CLSCompliant(AssemblyInfo.ClsCompliant)]


[assembly:AssemblyConfiguration("")]
[assembly:AssemblyTrademark("")]
[assembly:AssemblyCulture("")]
