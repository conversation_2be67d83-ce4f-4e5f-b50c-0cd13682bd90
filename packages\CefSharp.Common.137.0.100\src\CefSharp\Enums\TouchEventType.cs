// Copyright © 2019 The CefSharp Authors. All rights reserved.
//
// Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.

namespace CefSharp.Enums
{
    /// <summary>
    /// Touch Event Type
    /// </summary>
    public enum TouchEventType
    {
        /// <summary>
        /// An enum constant representing the released option.
        /// </summary>
        Released = 0,
        /// <summary>
        /// An enum constant representing the pressed option.
        /// </summary>
        Pressed,
        /// <summary>
        /// An enum constant representing the moved option.
        /// </summary>
        Moved,
        /// <summary>
        /// An enum constant representing the cancelled option.
        /// </summary>
        Cancelled
    }
}
